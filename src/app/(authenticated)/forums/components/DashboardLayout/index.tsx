'use client';

import React, { useState, useEffect } from 'react';
import ProfileSidebar from '../ProfileSidebar';
import AnonymousProfileSidebar from '../AnonymousProfileSidebar';
import MobileAppPromotion from '../MobileAppPromotion';
import { ProfileSidebarSkeleton } from '../Shimmer';
import { DashboardLayoutPropsI } from './types';
import { Header } from '@/components';
import ForumQuestions from '../ForumQuestions';
import useAuth from '@/hooks/useAuth';

const DashboardLayout = ({ user }: DashboardLayoutPropsI) => {
  const [isMounted, setIsMounted] = useState(false);
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  const renderProfileSidebar = (isMobile: boolean) => {
    if (isLoading) {
      return <ProfileSidebarSkeleton isMobile={isMobile} />;
    }

    if (isAuthenticated && user) {
      return <ProfileSidebar user={user} isMobile={isMobile} />;
    }

    return <AnonymousProfileSidebar isMobile={isMobile} />;
  };

  return (
    <>
      <Header />
      <main className="py-20">
        <div className="max-w-screen-xl mx-auto px-4">
          {/* Mobile ProfileSidebar */}
          <div className="lg:hidden mb-6">{renderProfileSidebar(true)}</div>

          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            {/* Desktop ProfileSidebar */}
            <div className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20">{renderProfileSidebar(false)}</div>
            </div>

            <ForumQuestions />

            <aside className="hidden lg:block lg:col-span-3">
              <div className="sticky top-20 space-y-6">
                <MobileAppPromotion />
              </div>
            </aside>
          </div>
        </div>
      </main>
    </>
  );
};

export default DashboardLayout;
