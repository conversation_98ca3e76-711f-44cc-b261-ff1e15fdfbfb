'use client';
import React from 'react';
import Link from 'next/link';
import { AnonymousProfileSidebarPropsI } from './types';

const AnonymousProfileSidebar = ({
  isMobile = false,
}: AnonymousProfileSidebarPropsI) => {
  if (isMobile) {
    // Mobile horizontal layout
    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
        <div className="p-4">
          <div className="flex items-center justify-between space-x-4">
            {/* Avatar */}
            <div className="flex-shrink-0">
              <div className="w-14 h-14 rounded-full border-2 border-gray-200 bg-gray-100 flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                Anonymous User
              </h3>
              <p className="text-xs text-gray-500">
                Sign in to see your profile
              </p>
            </div>

            {/* Sign In Button */}
            <div className="flex-shrink-0">
              <Link
                href="/login"
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600]"
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Desktop vertical layout
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
      <div className="h-16 bg-gradient-to-r from-gray-900 to-[#448600] relative"></div>

      <div className="px-4 pb-4 -mt-8 relative">
        <div className="flex justify-center mb-4">
          <div className="relative">
            <div className="w-18 h-18 rounded-full border-4 border-white bg-gray-100 flex items-center justify-center">
              <svg
                className="w-10 h-10 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="text-center mb-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-1">
            Anonymous User
          </h2>
          <p className="text-sm text-gray-500">Sign in to see your profile</p>
        </div>

        <div className="border-t border-gray-200 my-4"></div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-3">Join our community</p>
          <div className="space-y-2">
            <Link
              href="/signup"
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600]"
            >
              Sign Up
            </Link>
            <Link
              href="/login"
              className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600]"
            >
              Sign In
            </Link>
          </div>
        </div>

        <div className="border-t border-gray-200 my-4"></div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">Expand your network</p>
          <div className="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded">
            <div className="w-4 h-4 bg-primary rounded-sm mr-2 flex-shrink-0"></div>
            <span className="text-sm font-semibold text-gray-800">
              Join Navicater App
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnonymousProfileSidebar;
