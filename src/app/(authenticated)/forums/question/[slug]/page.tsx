'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  fetchQuestionByIdAPI,
  fetchQuestionIdBySlug,
} from '@/networks/forum/questionDetail';
import QuestionDetailCard from './components/QuestionDetailCard';
import { QuestionCardSkeleton } from '../../components/Shimmer';
import AnswerCard from './components/AnswerCard';
import { QuestionDetailI } from '@/networks/forum/types';
import AuthOverlay from '@/components/AuthOverlay';
import useAuth from '@/hooks/useAuth';

const QuestionDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const slug = params.slug as string;
  const { isAuthenticated } = useAuth();

  const [question, setQuestion] = useState<QuestionDetailI | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!slug) return;

      try {
        setLoading(true);
        setError(null);
        const { id } = await fetchQuestionIdBySlug(slug);
        const questionData = await fetchQuestionByIdAPI(id);
        setQuestion(questionData);
      } catch (err) {
        console.error('Error fetching question details:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to load question'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [slug]);

  const handleAnswer = (questionId: string) => {
    console.log('Answer question:', questionId);
  };

  const handleAnswerUpvote = (answerId: string) => {
    console.log('Upvote answer:', answerId);
  };

  const handleAnswerDownvote = (answerId: string) => {
    console.log('Downvote answer:', answerId);
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6">
        <QuestionCardSkeleton />
        <div className="mt-6 space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse"
            >
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Failed to load question
          </h3>
          <p className="text-sm text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#448600] hover:bg-[#357000] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#448600]"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!question) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Question not found
          </h3>
          <p className="text-sm text-gray-600">
            The question you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-6">
      <div className="mb-6">
        <button
          onClick={() => router.back()}
          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back to Questions
        </button>
      </div>

      {/* Question Detail Card */}
      <QuestionDetailCard
        question={question}
        onUpvote={id => console.log('Upvote question:', id)}
        onDownvote={id => console.log('Downvote question:', id)}
        onAnswer={handleAnswer}
      />

      {/* Answers Section */}
      <div className="mt-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {question.answers?.length || 0}{' '}
            {(question.answers?.length || 0) === 1 ? 'Answer' : 'Answers'}
          </h2>
        </div>

        {question.answers && question.answers.length > 0 ? (
          <div className="space-y-4">
            {question.answers.map(answer => (
              <AnswerCard
                key={answer.id}
                answer={answer}
                onUpvote={handleAnswerUpvote}
                onDownvote={handleAnswerDownvote}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <div className="text-gray-400 mb-2">
              <svg
                className="w-12 h-12 mx-auto"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No answers yet
            </h3>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuestionDetailPage;
