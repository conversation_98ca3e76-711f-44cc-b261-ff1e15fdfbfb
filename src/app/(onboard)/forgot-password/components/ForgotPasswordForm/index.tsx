'use client';
import { Button, Input } from '@/components';
import Link from 'next/link';
import useForgotPasswordForm from './useHook';

function ForgotPasswordForm() {
  const {
    formData,
    errors,
    isLoading,
    isSubmitted,
    handleInputChange,
    handleSubmit,
  } = useForgotPasswordForm();

  if (isSubmitted) {
    return (
      <div className="text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h2 className="text-lg font-medium text-gray-900 mb-2">
            Check your email
          </h2>
          <p className="text-sm text-gray-600">
            We've sent a password reset link to{' '}
            <strong>{formData.email}</strong>
          </p>
        </div>
        <Link
          href="/login"
          className="text-[#0077B5] hover:underline text-sm font-medium"
        >
          Back to sign in
        </Link>
      </div>
    );
  }
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <Input
          id="email"
          name="email"
          type="text"
          autoComplete="username"
          required
          value={formData.email}
          onChange={handleInputChange}
          placeholder="Email"
          className="w-full"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email}</p>
        )}
      </div>

      <div className="text-sm text-gray-600 leading-relaxed">
        We'll send a verification code to this email.
      </div>

      {errors.general && (
        <div className="text-sm text-red-600">{errors.general}</div>
      )}

      <div className="space-y-4">
        <Button
          className="w-full rounded-full bg-[#0077B5] hover:bg-[#005885] text-white text-base font-semibold py-3"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Sending...' : 'Next'}
        </Button>

        <div className="text-center">
          <Link
            href="/login"
            className="text-gray-600 hover:text-gray-800 text-sm font-medium"
          >
            Back
          </Link>
        </div>
      </div>
    </form>
  );
}
export default ForgotPasswordForm;
