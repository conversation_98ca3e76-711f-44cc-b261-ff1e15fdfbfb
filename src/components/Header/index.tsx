'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Logo from '../Logo';
import { CONFIG } from '../../constants/config';
import ForumIcon from '@assets/svg/Forum';

const Header = () => {
  const pathname = usePathname();
  const isForumsActive = pathname === '/' || pathname === '/forums';

  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm"
      style={{ height: CONFIG.layout.headerHeight }}
    >
      <div className="max-w-screen-xl mx-auto px-4 h-full">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Logo compact={false} />
            </Link>
          </div>

          <div className="flex items-center">
            {/* Forums Navigation */}
            <Link
              href="/"
              className={`flex items-center justify-center px-4 py-2 transition-colors duration-200 ${
                isForumsActive ? ' broder-primary  border-b-1 ' : ''
              }`}
            >
              <ForumIcon size={20} className="mr-2" />
              {/* <span className="text-sm font-medium">Forums</span> */}
            </Link>

          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
